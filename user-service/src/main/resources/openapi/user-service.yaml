openapi: 3.0.0
info:
  title: User-service DEUSS API
  version: 2.1.1
  description: API for managing user authentication with passkeys
tags:
  - name: Passkey Registration
    description: Passkey - password less authentication method registration.
paths:
  /users/{id}:
    get:
      tags:
        - Users
      summary: Get user by ID
      operationId: getUserById
      parameters:
        - name: id
          in: path
          description: ID of the user to get
          required: true
          schema:
            type: string
            format: uuid
      responses:
        "200":
          description: User details returned successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UserData"
        "404":
          $ref: "./common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
    patch:
      tags:
        - Users
      summary: Update user by ID
      operationId: updateUserById
      parameters:
        - name: id
          in: path
          description: ID of the user to get
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        $ref: "#/components/requestBodies/UpdateUserRequest"
      responses:
        "200":
          description: User updated successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UserData"
        "400":
          $ref: "./common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        "404":
          $ref: "./common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
        "409":
          $ref: "./common_constructs_openapi.yaml#/components/responses/ConflictErrorResponse"

  /find:
    post:
      tags:
        - Users
      summary: Get UUID by email
      operationId: findUserIdByEmail
      security:
        - bearerAuth: [ ]
      requestBody:
        $ref: "#/components/requestBodies/UserFindByEmailRequest"
      responses:
        "200":
          description: User found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/FindUserByEmailResponse"
        "404":
          $ref: "./common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"

  /auth/register:
    post:
      tags:
        - User Registration
      summary: Register a new user using password authentication.
      operationId: registerUser
      requestBody:
        $ref: "#/components/requestBodies/UserRegistrationRequest"
      responses:
        "201":
          description: User registered successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RegistrationSuccessResponse"
        "400":
          $ref: "./common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        "409":
          $ref: "./common_constructs_openapi.yaml#/components/responses/ConflictErrorResponse"

  /auth/register/passkey/start:
    post:
      tags:
        - User Passkey Registration
      summary: Initiate User Registration process using passkeys.
      operationId: start_user_registration
      requestBody:
        $ref: "#/components/requestBodies/UserRegistrationPasskeyStartRequest"
      responses:
        "200":
          description: Registration options returned successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RegistrationOptions"
        "400":
          $ref: "./common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        "409":
          $ref: "./common_constructs_openapi.yaml#/components/responses/ConflictErrorResponse"

  /auth/login:
    post:
      tags:
        - User Authentication
      summary: Authenticate user using password authentication.
      operationId: loginUser
      requestBody:
        $ref: "#/components/requestBodies/UserLoginRequest"
      responses:
        "200":
          description: User authenticated successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/FinishLoginResponse"
        "400":
          $ref: "./common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        "401":
          $ref: "./common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"

  /auth/login/passkey/start:
    post:
      tags:
        - Passkey Authentication
      summary: Initiate passkey login process
      operationId: startLogin
      responses:
        "200":
          description: Login options returned successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LoginOptions"
        "400":
          $ref: "./common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        "404":
          $ref: "./common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"

  /auth/login/passkey/finish:
    post:
      tags:
        - Passkey Authentication
      summary: Complete passkey login process
      operationId: finishLogin
      requestBody:
        $ref: "#/components/requestBodies/LoginFinish"
      responses:
        "200":
          description: Login successful
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/FinishLoginResponse"
        "400":
          $ref: "./common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        "401":
          $ref: "./common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"

  /auth/register/passkey/finish:
    post:
      tags:
        - User Passkey Registration
      summary: Complete user registration and create user.
      operationId: finish_user_registration
      requestBody:
        $ref: "#/components/requestBodies/FinishUserRegistrationRequest"
      responses:
        "201":
          description: User created and passkey registered successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RegistrationSuccessResponse"
        "400":
          $ref: "./common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        "409":
          $ref: "./common_constructs_openapi.yaml#/components/responses/ConflictErrorResponse"

  /auth/token/refresh:
    post:
      tags:
        - Token Management
      summary: Refresh access token using a refresh token
      operationId: refresh_token
      requestBody:
        $ref: "#/components/requestBodies/RefreshTokenRequest"
      responses:
        "200":
          description: Token refreshed successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RefreshTokenResponse"
        "400":
          $ref: "./common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        "401":
          $ref: "./common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"

  /auth/password/change:
    post:
      tags:
        - Password Management
      summary: Change user password
      operationId: change_password
      requestBody:
        $ref: "#/components/requestBodies/ChangePasswordRequest"
      responses:
        "200":
          description: Password changed successfully
        "400":
          $ref: "./common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        "401":
          $ref: "./common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"

  /auth/passkey/start:
    post:
      tags:
        - Passkey Registration
      summary: Initiate additional passkey registration process for registered user.
      operationId: start_additional_passkey_registration
      responses:
        "200":
          description: Options related to additional passkey registration for registered user.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StartAdditionalPasskeyRegistrationResponse"
        "400":
          $ref: "./common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        "401":
          $ref: "./common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        "409":
          $ref: "./common_constructs_openapi.yaml#/components/responses/ConflictErrorResponse"

  /auth/passkey/finish:
    post:
      tags:
        - Passkey Registration
      summary: Finalize additional passkey registration process for registered user.
      operationId: finish_additional_passkey_registration
      requestBody:
        $ref: "#/components/requestBodies/FinishAdditionalPasskeyRegistrationRequest"
      responses:
        "201":
          description: Additional passkey registered successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/FinishAdditionalPasskeyRegistrationResponse"
        "400":
          $ref: "./common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        "401":
          $ref: "./common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        "409":
          $ref: "./common_constructs_openapi.yaml#/components/responses/ConflictErrorResponse"

  /auth/passkey:
    get:
      summary: Get all registered passkey credentials of the user.
      operationId: get_user_passkey_credentials
      tags:
        - Get Passkey
      security:
        - bearerAuth: [ ]
      responses:
        "200":
          description: All registered passkey credentials of the user.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetAllUserPasskeyCredentialsResponse"
        "401":
          $ref: "./common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"

  /auth/passkey/{id}:
    delete:
      summary: Delete registered passkey of the currently logged user.
      operationId: delete_user_passkey_credential
      tags:
        - Delete Passkey
      parameters:
        - name: id
          in: path
          description: ID of passkey credential
          required: true
          schema:
            type: string
            format: uuid
      security:
        - bearerAuth: [ ]
      responses:
        "200":
          description: Remaining user passkey credentials.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DeleteUserPasskeyCredentialResponse"
        "401":
          $ref: "./common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        "404":
          description: "Passkey credential does not exist for currently logged user."
          $ref: "./common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"

  /admin/invites/generate:
    post:
      tags:
        - Invites Management
      summary: Generate new invitation tokens
      operationId: generateInviteToken
      security:
        - bearerAuth: [ ]
      requestBody:
        $ref: "#/components/requestBodies/InviteTokenGenerateRequest"
      responses:
        "200":
          description: Invite token(s) generated successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/InviteTokenGenerateResponse"
        "400":
          $ref: "./common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        "401":
          $ref: "./common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"

  /invites/verify:
    post:
      tags:
        - Invites Management
      summary: Verify validity of an invitation token
      operationId: verifyInviteToken
      requestBody:
        $ref: "#/components/requestBodies/InviteTokenVerifyRequest"
      responses:
        "200":
          description: Invite token is valid
        "400":
          $ref: "./common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        "401":
          $ref: "./common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"

  /admin/invites/{token}:
    delete:
      tags:
        - Invites Management
      summary: Cancel validity of an invitation token
      operationId: revokeInviteToken
      security:
        - bearerAuth: [ ]
      parameters:
        - name: token
          in: path
          description: UUID of token to revoke
          required: true
          schema:
            type: string
            format: uuid
      responses:
        "204":
          description: Invite token successfully revoked
        "400":
          $ref: "./common_constructs_openapi.yaml#/components/responses/BadRequestErrorResponse"
        "401":
          $ref: "./common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"

  /admin/users/{id}/roles/admin:
    post:
      tags:
        - User Role Management
      summary: Add admin role to a user. Invoking user must have ADMIN role.
      operationId: add_admin_role
      security:
        - bearerAuth: [ ]
      parameters:
        - name: id
          in: path
          description: ID of the user to add admin role to
          required: true
          schema:
            type: string
            format: uuid
      responses:
        "200":
          description: Admin role added successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AssignAdminRoleResponse"
        "401":
          $ref: "./common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        "403":
          $ref: "./common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        "404":
          $ref: "./common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
    delete:
      tags:
        - User Role Management
      summary: Remove admin role from a user. Invoking user must have ADMIN role.
      operationId: remove_admin_role
      security:
        - bearerAuth: [ ]
      parameters:
        - name: id
          in: path
          description: ID of the user to remove admin role from
          required: true
          schema:
            type: string
            format: uuid
      responses:
        "200":
          description: Admin role removed successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UnassignAdminRoleResponse"
        "401":
          $ref: "./common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        "403":
          $ref: "./common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        "404":
          $ref: "./common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
        "409":
          $ref: "./common_constructs_openapi.yaml#/components/responses/ConflictErrorResponse"

  /users/{id}/roles/originator:
    post:
      tags:
        - User Role Management
      summary: Add ORIGINATOR role to a user. Invoking user must have ADMIN or ORIGINATOR role.
      operationId: add_originator_role
      security:
        - bearerAuth: [ ]
      parameters:
        - name: id
          in: path
          description: ID of the user to add originator role to
          required: true
          schema:
            type: string
            format: uuid
      responses:
        "200":
          description: Originator role added successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AssignAdminRoleResponse"
        "401":
          $ref: "./common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        "403":
          $ref: "./common_constructs_openapi.yaml#/components/responses/ForbiddenErrorResponse"
        "404":
          $ref: "./common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
    delete:
      tags:
        - User Role Management
      summary: Remove ORIGINATOR role from a user. Invoking user must have ADMIN or ORIGINATOR role.
      operationId: remove_originator_role
      security:
        - bearerAuth: [ ]
      parameters:
        - name: id
          in: path
          description: ID of the user to remove originator role from
          required: true
          schema:
            type: string
            format: uuid
      responses:
        "200":
          description: Originator role removed successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UnassignAdminRoleResponse"
        "401":
          $ref: "./common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        "403":
          $ref: "./common_constructs_openapi.yaml#/components/responses/UnauthorizedErrorResponse"
        "404":
          $ref: "./common_constructs_openapi.yaml#/components/responses/NotFoundErrorResponse"
        "409":
          $ref: "./common_constructs_openapi.yaml#/components/responses/ConflictErrorResponse"

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  requestBodies:
    UpdateUserRequest:
      description: Request to update user details
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              first_name:
                type: string
                description: User's first name
              last_name:
                type: string
                description: User's last name
              phone_number:
                type: string
                description: User's phone number
              residence_country:
                type: string
                description: User's country of residence
              birth_date:
                type: string
                format: date
                description: User's date of birth
            minProperties: 1
    UserRegistrationRequest:
      description: Request to register a new user
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              email:
                type: string
                format: email
                description: User's email address
              password:
                type: string
                format: password
                description: User's password
              inviteToken:
                type: string
                format: uuid
              userData:
                $ref: "#/components/schemas/UserRegistrationData"
            required:
              - email
              - password
              - inviteToken
              - userData
    UserRegistrationPasskeyStartRequest:
      description: Initial registration request with user email
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/RegistrationStartRequest"
    UserLoginRequest:
      description: Request to login a user with email and password
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/UserLoginRequest"
    UserFindByEmailRequest:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              email:
                type: string
                format: email
                description: Email address of the user to find.
            required:
              - email
    LoginFinish:
      description: Final login payload with authentication assertion
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/LoginFinishRequest"
    FinishUserRegistrationRequest:
      description: Final registration payload with user data and attestation
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/FinishUserRegistrationRequest"
    ChangePasswordRequest:
      description: Request to change user password
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ChangePasswordRequest"
    RefreshTokenRequest:
      description: Request to refresh an access token
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/TokenRefreshRequest"
    InviteTokenVerifyRequest:
      description: Request to verify an invite token
      required: true
      content:
        application/json:
          schema:
            type: string
            format: uuid
    InviteTokenRevokeRequest:
      description: Request to revoke an invite token
      required: true
      content:
        application/json:
          schema:
            type: string
            format: uuid
    InviteTokenGenerateRequest:
      description: Request to generate an invite token
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/InviteTokenGenerateRequest"
    FinishAdditionalPasskeyRegistrationRequest:
      description: Additional Passkey registration request.
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/FinishAdditionalPasskeyRegistrationRequest"

  schemas:
    UserData:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the user
        email:
          type: string
          format: email
          description: User's email address
        first_name:
          type: string
          description: User's first name
        last_name:
          type: string
          description: User's last name
        phone_number:
          type: string
          description: User's phone number
        residence_country:
          type: string
          description: User's country of residence
        birth_date:
          type: string
          format: date
          description: User's date of birth
        created_at:
          type: string
          format: date-time
          description: Timestamp when the user was created
        updated_at:
          type: string
          format: date-time
          description: Timestamp when the user was last updated
        roles:
          type: array
          uniqueItems: true
          items:
            $ref: "#/components/schemas/UserRole"
          description: Array field of possible user roles
      required:
        - id
        - email
        - first_name
        - last_name
        - phone_number
        - residence_country
        - birth_date
        - created_at
        - updated_at
        - roles

    RegistrationStartRequest:
      type: object
      properties:
        email:
          type: string
          format: email
          description: Email address of the user
          example: <EMAIL>
          x-field-extra-annotation: "@NotBlank"
        inviteToken:
          type: string
          format: uuid
      required:
        - email
        - inviteToken

    FindUserByEmailResponse:
      type: object
      properties:
        user_id:
          type: string
          format: uuid
      required:
        - user_id

    RegistrationOptions:
      type: object
      properties:
        session:
          type: string
          description: Session ID for the registration process
          format: uuid
        challenge:
          type: string
          description: Cryptographic challenge for registration
        rp:
          type: object
          description: Relaying Party
          properties:
            name:
              description: "Name of Relaying party"
              type: string
            id:
              description: "Relaying Party ID - used by client to match Public Key Credential Source"
              type: string
          required:
            - name
            - id
        user:
          type: object
          properties:
            id:
              type: string
            name:
              type: string
            displayName:
              type: string
          required:
            - id
            - name
            - displayName
        pubKeyCredParams:
          type: array
          items:
            type: object
            properties:
              type:
                type: string
              alg:
                description: See https://www.iana.org/assignments/cose/cose.xhtml for actual alg name
                type: number
          required:
            - type
            - alg
        timeout:
          type: integer
        attestation:
          type: string
        excludeCredentials:
          type: array
          description: Don’t re-register any authenticator that has one of these credentials
          items:
            type: object
            properties:
              type:
                type: string
              id:
                type: string
              transports:
                type: array
                items:
                  type: string
            required:
              - type
              - id
              - transports
        authenticatorSelection:
          type: object
          properties:
            authenticatorAttachment:
              type: string
            residentKey:
              type: string
            userVerification:
              type: string
          required:
            - authenticatorAttachment
            - residentKey
            - userVerification

      required:
        - session
        - challenge
        - rp
        - user
        - pubKeyCredParams
        - timeout
        - attestation
        - excludeCredentials
        - authenticatorSelection

    UserLoginRequest:
      type: object
      properties:
        email:
          type: string
          format: email
          description: User's email address
        password:
          type: string
          format: password
          description: User's password
      required:
        - email
        - password

    LoginOptions:
      type: object
      properties:
        session:
          type: string
          description: Session ID for the login process
        challenge:
          type: string
          description: Cryptographic challenge for login
        allowCredentials:
          type: array
          items:
            $ref: "#/components/schemas/CredentialDescriptor"
        rpId:
          type: string
          description: Relying party ID
        timeout:
          type: integer
          description: Time in milliseconds to wait for completion
      required:
        - session
        - challenge
        - allowCredentials
        - rpId
        - timeout

    CredentialDescriptor:
      type: object
      properties:
        type:
          type: string
          description: Type of the credential
        id:
          type: string
          description: Base64URL encoded credential ID
        transports:
          type: array
          description: See https://www.w3.org/TR/webauthn-2/#enum-transport
          items:
            type: string
            enum: [ usb, nfc, ble, internal ]
      required:
        - type
        - id
        - transports

    LoginFinishRequest:
      type: object
      properties:
        session:
          type: string
          description: Session ID from start login response
        assertionResponse:
          $ref: "#/components/schemas/AuthenticationResponse"
      required:
        - session
        - assertionResponse

    AuthenticationResponse:
      type: object
      properties:
        id:
          type: string
          description: Base64URL encoded credential ID
        rawId:
          type: string
          description: Base64URL encoded credential ID
        response:
          $ref: "#/components/schemas/AuthenticatorAssertionResponse"
      required:
        - id
        - rawId
        - response

    AuthenticatorAssertionResponse:
      type: object
      properties:
        clientDataJSON:
          type: string
          format: byte
        authenticatorData:
          type: string
          format: byte
        signature:
          type: string
          format: byte
        userHandle:
          type: string
          format: byte
      required:
        - clientDataJSON
        - authenticatorData
        - signature

    UserInfo:
      type: object
      properties:
        id:
          format: uuid
          type: string
        email:
          format: email
          type: string
        roles:
          type: array
          uniqueItems: true
          items:
            $ref: "#/components/schemas/UserRole"
          description: Array field of possible user roles
      required:
        - id
        - email
        - roles

    FinishPasskeyRegistrationInput:
      type: object
      properties:
        session:
          description: Registration session ID
          type: string
          format: uuid
        attestationResponse:
          $ref: "#/components/schemas/AttestationResponse"
        friendlyName:
          description: Passkey friendly name - optionally provided by user to recognize passkey.
          type: string
          example: "Pixel 9"
          maxLength: 128
          x-field-extra-annotation: "@cz.deuss.platform.offchain.framework.validation.NotBlankOrNull"
      required:
        - session
        - userData
        - attestationResponse
    FinishUserRegistrationRequest:
      type: object
      properties:
        # TODO temporary - backward compatibility - then remove
        session:
          description: Registration session ID
          type: string
          format: uuid
          # TODO temporary - backward compatibility - then remove
        attestationResponse:
          $ref: "#/components/schemas/AttestationResponse"
        passkey:
          $ref: "#/components/schemas/FinishPasskeyRegistrationInput"
        userData:
          $ref: "#/components/schemas/UserRegistrationData"
      required:
        # TODO will be required after migration
        #        - passkey
        - userData

    UserRegistrationData:
      type: object
      properties:
        first_name:
          type: string
        last_name:
          type: string
        phone_number:
          type: string
        residence_country:
          type: string
        birth_date:
          type: string
          format: date
      required:
        - first_name
        - last_name
        - phone_number
        - residence_country
        - birth_date

    AttestationResponse:
      type: object
      properties:
        clientDataJSON:
          type: string
          format: byte
        attestationObject:
          type: string
          format: byte
        transports:
          type: array
          items:
            type: string
      required:
        - clientDataJSON
        - attestationObject

    RegistrationSuccessResponse:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: User ID
        email:
          type: string
          format: email
          description: User email
      required:
        - id
        - email

    TokenRefreshRequest:
      type: object
      properties:
        refresh_token:
          type: string
          description: Refresh token obtained during authentication
      required:
        - refresh_token

    AccessRefreshToken:
      properties:
        access_token:
          type: string
          description: JWT access token
        refresh_token:
          type: string
          description: Refresh token for obtaining a new access token
        token_type:
          type: string
          description: Type of token (Bearer)
          example: Bearer
        expires_in:
          type: integer
          minimum: 1
          description: Token expiration time in seconds
      required:
        - access_token
        - refresh_token
        - token_type
        - expires_in

    FinishLoginResponse:
      type: object
      properties:
        tokens:
          $ref: "#/components/schemas/AccessRefreshToken"
        user:
          $ref: "#/components/schemas/UserData"
      required:
        - tokens
        - user

    ChangePasswordRequest:
      type: object
      properties:
        email:
          type: string
          format: email
          description: User's email address
        new_password:
          type: string
          format: password
          description: User's new password
        invite_token:
          type: string
          format: uuid
          description: invite token for password change
      required:
        - email
        - new_password
        - invite_token

    RefreshTokenResponse:
      type: object
      properties:
        tokens:
          $ref: "#/components/schemas/AccessRefreshToken"
        user:
          $ref: "#/components/schemas/UserData"
      required:
        - tokens
        - user

    InviteTokenGenerateResponse:
      type: object
      properties:
        invite_tokens:
          type: array
          description: List of invite tokens (UUIDs)
          items:
            type: string
            format: uuid
      required:
        - invite_tokens

    InviteTokenGenerateRequest:
      type: object
      properties:
        count:
          type: integer
          description: number of tokens to be generated
          minimum: 1
          maximum: 1000
      required:
        - count

    UserRole:
      type: string
      example: USER
      enum:
        - USER
        - ADMIN
        - ORIGINATOR
    StartAdditionalPasskeyRegistrationResponse:
      description: "Response for register additional user passkey credential."
      properties:
        options:
          $ref: "#/components/schemas/RegistrationOptions"
      required:
        - options

    FinishAdditionalPasskeyRegistrationRequest:
      type: object
      description: "Request for register additional user passkey credential."
      properties:
        passkey:
          $ref: "#/components/schemas/FinishPasskeyRegistrationInput"
      required:
        - passkey

    FinishAdditionalPasskeyRegistrationResponse:
      type: object
      properties:
        passkey:
          $ref: "#/components/schemas/PasskeyCredential"
          description: Registered Passkey credential.
      required:
        - passkey
    PasskeyCredential:
      properties:
        id:
          description: Passkey id. Is not credential ID.
          nullable: false
          type: string
          format: uuid
        friendly_name:
          type: string
          nullable: false
        last_used_at:
          type: string
          format: date-time
          nullable: false
        last_updated_at:
          type: string
          format: date-time
          nullable: false
      required:
        - friendly_name
        - id
        - last_used_at
        - last_updated_at
    GetAllUserPasskeyCredentialsResponse:
      properties:
        passkey_credentials:
          type: array
          items:
            $ref: "#/components/schemas/PasskeyCredential"
      required:
        - passkey_credentials

    DeleteUserPasskeyCredentialResponse:
      properties:
        passkey_credentials:
          type: array
          items:
            $ref: "#/components/schemas/PasskeyCredential"
      required:
        - passkey_credentials
    RoleAssignment:
      properties:
        assignee_id:
          type: string
          format: uuid
        assigner_id:
          type: string
          format: uuid
          description: ID of assigner - sometimes can be self.
        assigned_at:
          type: string
          format: date-time
        role:
          $ref: "#/components/schemas/UserRole"
      required:
        - assignee_id
        - assigner_id
        - assigned_at
        - role

    AssignAdminRoleResponse:
      properties:
        role_assignment:
          $ref: "#/components/schemas/RoleAssignment"
      required:
        - role_assignment
    UnassignAdminRoleResponse:
      properties:
        unassigned_role:
          $ref: "#/components/schemas/UserRole"
      required:
        - unassigned_role
