package cz.deuss.userservice.configuration

import com.yubico.webauthn.RelyingParty
import com.yubico.webauthn.data.RelyingPartyIdentity
import com.yubico.webauthn.data.AttestationConveyancePreference
import cz.deuss.userservice.adapter.CredentialRepositoryAdapter
import cz.deuss.userservice.database.repository.PasskeyRepository
import cz.deuss.userservice.database.repository.UserRepository
import io.micronaut.context.annotation.Bean
import io.micronaut.context.annotation.Factory
import jakarta.inject.Singleton

@Factory
class WebAuthnConfig {

    @Bean
    @Singleton
    fun relyingParty(
        passkeyProperties: PasskeyProperties,
        passkeyRepository: PasskeyRepository,
        userRepository: UserRepository
    ): RelyingParty {
        val rpIdentity = RelyingPartyIdentity.builder()
            .id(passkeyProperties.rpId)
            .name(passkeyProperties.rpName)
            .build()

        return RelyingParty.builder()
            .identity(rpIdentity)
            .credentialRepository(CredentialRepositoryAdapter(passkeyRepository, userRepository))
            .origins(setOf(passkeyProperties.origin))
            .attestationConveyancePreference(AttestationConveyancePreference.NONE)
            .build()
    }
}