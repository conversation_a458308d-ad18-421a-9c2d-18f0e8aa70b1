package cz.deuss.userservice.user.role

import cz.deuss.platform.offchain.framework.authentication.Role

/**
 * Hierarchy of assigned roles. For example by assignation of "ADMIN" role user will also inherit role "USER", ...
 * This class hold such mapping.
 */
data object RoleHierarchy {

    /**
     * For [role] return directly inherited roles - returned roles must be further expanded before actual use.
     */
    fun directlyInheritedRoles(role: Role.Enum): Set<Role.Enum> {
        // no need to list "self" as role in inherited roles.
        return when (role) {
            Role.Enum.USER -> setOf()
            Role.Enum.ORIGINATOR -> setOf(Role.Enum.USER)
            Role.Enum.ADMIN -> setOf(Role.Enum.USER,Role.Enum.ORIGINATOR)
        }
    }
}
