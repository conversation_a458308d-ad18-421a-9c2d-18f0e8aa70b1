package cz.deuss.userservice.adapter

import cz.deuss.platform.offchain.framework.authentication.Role
import cz.deuss.platform.offchain.framework.exceptions.ConflictException
import cz.deuss.platform.offchain.framework.exceptions.NotFoundException
import cz.deuss.userservice.api.model.UnassignAdminRoleResponse
import cz.deuss.userservice.domain.user.UserId
import cz.deuss.userservice.mapping.toUserRole
import cz.deuss.userservice.service.UserService
import cz.deuss.userservice.user.role.AssignedUserRolesService
import cz.deuss.userservice.user.role.ManageUserRoleService
import cz.deuss.userservice.validation.Invalid
import cz.deuss.userservice.validation.Valid
import io.micronaut.transaction.annotation.Transactional
import jakarta.inject.Singleton
import java.util.UUID

/**
 * Adapts REST API <-> Unassign role service layer.
 */
@Singleton
open class UnassignRoleRestAdapter(
    private val userService: UserService,
    private val manageUserRoleService: ManageUserRoleService,
    private val assignedUserRolesService: AssignedUserRolesService,
) {

    @Transactional
    open fun unassignRole(potentialAssignee: UUID, toUnassign: Role.Enum): UnassignAdminRoleResponse {
        val assigneeId = validateAssigneeExists(potentialAssignee)

        val assignedRoles = assignedUserRolesService.assignedRoles(assigneeId)
        if (assignedRoles.hasNot(toUnassign)) {
            throw ConflictException("Assignee does not have assigned role $toUnassign.")
        }

        manageUserRoleService.unassign(assigneeId, toUnassign)

        return UnassignAdminRoleResponse(
            unassignedRole = toUnassign.toUserRole()
        )
    }

    private fun validateAssigneeExists(potentialAssignee: UUID): UserId {
        return when (val result = userService.validateExists(potentialAssignee)) {
            is Invalid -> throw NotFoundException("Assignee=$potentialAssignee does not found.")
            is Valid -> result.data
        }
    }
}
