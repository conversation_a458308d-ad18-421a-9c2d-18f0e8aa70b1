package cz.deuss.userservice.adapter

import cz.deuss.platform.offchain.framework.authentication.AuthenticatedUserId
import cz.deuss.platform.offchain.framework.authentication.Role
import cz.deuss.platform.offchain.framework.exceptions.NotFoundException
import cz.deuss.userservice.api.model.AssignAdminRoleResponse
import cz.deuss.userservice.api.model.RoleAssignment
import cz.deuss.userservice.domain.user.UserId
import cz.deuss.userservice.domain.user.toUserId
import cz.deuss.userservice.mapping.toUserRole
import cz.deuss.userservice.service.UserService
import cz.deuss.userservice.user.role.AssignedUserRolesService
import cz.deuss.userservice.user.role.ManageUserRoleService
import cz.deuss.userservice.validation.Invalid
import cz.deuss.userservice.validation.Valid
import io.micronaut.transaction.annotation.Transactional
import jakarta.inject.Singleton
import java.util.UUID

/**
 * Adapts REST API <-> ADD admin role service layer.
 */
@Singleton
open class AssignRoleRestAdapter(
    private val userService: UserService,
    private val manageUserRoleService: ManageUserRoleService,
    private val assignedUserRolesService: AssignedUserRolesService,
    private val authenticatedUserId: () -> AuthenticatedUserId,
) {

    @Transactional
    open fun assignRole(potentialAssignee: UUID, role: Role.Enum): AssignAdminRoleResponse {
        val assignerId = authenticatedUserId()

        val assigneeId = validateAssigneeExists(potentialAssignee)

        manageUserRoleService.assignRole(assignerId.toUserId(), role, assigneeId)

        val assignment = assignedUserRolesService
            .listAssignments(assigneeId)
            .first { assignment -> assignment.role == role }

        return AssignAdminRoleResponse(
            roleAssignment = RoleAssignment(
                assigneeId = assignment.assignee.id,
                assignerId = assignment.assigner.id,
                assignedAt = assignment.assigned,
                role = assignment.role.toUserRole(),
            )
        )
    }

    private fun validateAssigneeExists(potentialAssignee: UUID): UserId {
        return when (val result = userService.validateExists(potentialAssignee)) {
            is Invalid -> throw NotFoundException("Assignee=$potentialAssignee does not found.")
            is Valid -> result.data
        }
    }
}
