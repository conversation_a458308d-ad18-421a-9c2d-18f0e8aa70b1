package cz.deuss.userservice.controller

import cz.deuss.platform.offchain.framework.annotations.ExposedController
import cz.deuss.platform.offchain.framework.authentication.Role
import cz.deuss.userservice.adapter.AssignRoleRestAdapter
import cz.deuss.userservice.adapter.UnassignRoleRestAdapter
import cz.deuss.userservice.api.UserRoleManagementApi
import cz.deuss.userservice.api.model.AssignAdminRoleResponse
import cz.deuss.userservice.api.model.UnassignAdminRoleResponse
import io.github.oshai.kotlinlogging.KotlinLogging
import io.micronaut.security.annotation.Secured
import java.util.UUID

@ExposedController
open class AdminRoleManagementController(
    private val assignAdminRoleAdapter: AssignRoleRestAdapter,
    private val unassignAdminRoleAdapter: UnassignRoleRestAdapter,
) : UserRoleManagementApi {

    private val logger = KotlinLogging.logger {}

    @Secured(Role.ADMIN)
    override fun addAdminRole(id: UUID): AssignAdminRoleResponse {
       return assignAdminRoleAdapter.assignRole(id, Role.Enum.ADMIN)
    }

    @Secured(Role.ADMIN)
    override fun removeAdminRole(id: UUID): UnassignAdminRoleResponse {
        return unassignAdminRoleAdapter.unassignRole(id, Role.Enum.ADMIN)
    }

    @Secured(value = [Role.ORIGINATOR, Role.ADMIN])
    override fun addOriginatorRole(id: UUID): AssignAdminRoleResponse {
        return assignAdminRoleAdapter.assignRole(id, Role.Enum.ORIGINATOR)
    }

    @Secured(value = [Role.ORIGINATOR, Role.ADMIN])
    override fun removeOriginatorRole(id: UUID): UnassignAdminRoleResponse {
        return unassignAdminRoleAdapter.unassignRole(id, Role.Enum.ORIGINATOR)
    }
}
