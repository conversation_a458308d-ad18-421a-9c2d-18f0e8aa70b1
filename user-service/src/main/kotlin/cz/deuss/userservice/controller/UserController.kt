package cz.deuss.userservice.controller

import cz.deuss.platform.offchain.framework.annotations.ExposedController
import cz.deuss.userservice.api.UsersApi
import cz.deuss.userservice.api.model.FindUserByEmailResponse
import cz.deuss.userservice.api.model.FindUserIdByEmailRequest
import cz.deuss.userservice.api.model.UpdateUserByIdRequest
import cz.deuss.userservice.api.model.UserData
import cz.deuss.userservice.service.UserService
import io.micronaut.scheduling.TaskExecutors
import io.micronaut.scheduling.annotation.ExecuteOn
import io.micronaut.security.annotation.Secured
import io.micronaut.security.rules.SecurityRule
import java.util.UUID


@ExposedController
@ExecuteOn(TaskExecutors.BLOCKING)
@Secured(SecurityRule.IS_AUTHENTICATED) // Secure all endpoints by default
open class UserController(
    private val userService: UserService
) : UsersApi {
    override fun getUserById(id: UUID): UserData {
        return userService.getUserById(id)
    }
    
    override fun updateUserById(id: UUID, updateUserByIdRequest: UpdateUserByIdRequest): UserData {
        return userService.updateUserById(id, updateUserByIdRequest)
    }

    override fun findUserIdByEmail(findUserIdByEmailRequest: FindUserIdByEmailRequest): FindUserByEmailResponse {
        return FindUserByEmailResponse(
            userId = userService.getUserIdByEmail(findUserIdByEmailRequest.email)
        )
    }
}
