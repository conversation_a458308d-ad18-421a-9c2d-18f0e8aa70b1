package cz.deuss.userservice.job

import cz.deuss.userservice.database.model.InviteToken
import cz.deuss.userservice.database.repository.InviteTokenRepository
import cz.deuss.userservice.database.repository.UserRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import io.micronaut.context.annotation.Value
import io.micronaut.context.event.ApplicationEventListener
import io.micronaut.runtime.event.ApplicationStartupEvent
import io.micronaut.transaction.annotation.Transactional
import jakarta.inject.Singleton
import java.time.Duration
import java.time.LocalDateTime

@Singleton
open class CreateAdminInviteTokenJob(
    private val inviteTokenRepository: InviteTokenRepository,
    private val userRepository: UserRepository,
    @Value("\${micronaut.security.token.generator.invite-token.expiration}")
    private val inviteTokenExpiry: Duration,
) : ApplicationEventListener<ApplicationStartupEvent> {

    private val logger = KotlinLogging.logger {}

    @Transactional
    override fun onApplicationEvent(event: ApplicationStartupEvent) {
        if (userRepository.count() != 0L) {
            logger.info { "Users database is not empty -> invitation token for admin won't be created." }
            return
        }

        if (inviteTokenRepository.countNotExpiredTokens(LocalDateTime.now()) != 0L) {
            logger.info { "Token database contains some not expired tokens -> invitation token for admin won't be created." }
            return
        }

        inviteTokenRepository.saveAndFlush(
            InviteToken(
                createdByUser = null,
                expiresAt = LocalDateTime.now().plus(inviteTokenExpiry)
            )
        ).also {
            logger.info { "Created new token for admin id#${it.id}" }
        }
    }


}