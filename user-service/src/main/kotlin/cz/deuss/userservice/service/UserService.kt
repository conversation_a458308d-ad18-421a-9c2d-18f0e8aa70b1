package cz.deuss.userservice.service

import cz.deuss.platform.offchain.framework.authentication.Role
import cz.deuss.platform.offchain.framework.exceptions.NotFoundException
import cz.deuss.userservice.api.model.UpdateUserByIdRequest
import cz.deuss.userservice.api.model.UserData
import cz.deuss.userservice.database.model.DeussUser
import cz.deuss.userservice.database.repository.UserRepository
import cz.deuss.userservice.domain.user.UserId
import cz.deuss.userservice.mapping.toUserData
import cz.deuss.userservice.user.role.AssignedUserRolesService
import cz.deuss.userservice.user.role.ManageUserRoleService
import cz.deuss.userservice.validation.Invalid
import cz.deuss.userservice.validation.Valid
import cz.deuss.userservice.validation.ValidationResult
import jakarta.inject.Singleton
import java.util.UUID

@Singleton
class UserService(
    private val userRepository: UserRepository,
    private val assignedUserRolesService: AssignedUserRolesService,
    private val manageUserRoleService: ManageUserRoleService,
) {

    fun create(user: DeussUser, roles: Set<Role.Enum>): DeussUser {
        val createdUser = userRepository.save(user)
        manageUserRoleService.selfAssignRoles(UserId(createdUser.id), roles)

        return createdUser
    }

    fun getUserById(id: UUID): UserData {
        val user = userRepository.getById(id)
            ?: throw NotFoundException("User not found with ID: $id")
        
        return user.toUserData()
    }

    fun findByEmailOrNull(email: String): UserData? {
        return userRepository.getByEmail(email)?.toUserData()
    }

    fun updateUserById(id: UUID, request: UpdateUserByIdRequest): UserData {
        val user = userRepository.getById(id)
            ?: throw NotFoundException("User not found with ID: $id")
        
        // Update only the fields that are provided in the request
        request.firstName?.let { user.firstName = it }
        request.lastName?.let { user.lastName = it }
        request.phoneNumber?.let { user.phoneNumber = it }
        request.residenceCountry?.let { user.residenceCountry = it }
        request.birthDate?.let { user.birthDate = it }
        
        // Save the updated user
        val savedUser = userRepository.update(user)
        
        // Return the UserInfo response
        return savedUser.toUserData()
    }

    fun validateExists(id: UUID): ValidationResult<UUID, UserId> {
        val user = userRepository.getById(id)

        return when (user) {
            null -> Invalid(id)
            else -> Valid(UserId(id))
        }
    }

    fun getUserIdByEmail(email: String): UUID {
       return userRepository.getIdByEmail(email) ?: throw NotFoundException("User not found with Email: $email")
    }

    private fun DeussUser.toUserData(): UserData {
        val roles = assignedUserRolesService.assignedRoles(UserId(id)).toSet()
        return toUserData(roles)
    }
}
