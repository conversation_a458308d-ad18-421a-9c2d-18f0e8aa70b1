package cz.deuss.userservice.service

import cz.deuss.platform.offchain.framework.exceptions.NotFoundException
import cz.deuss.userservice.database.model.InviteToken
import cz.deuss.userservice.database.repository.InviteTokenRepository
import cz.deuss.userservice.database.repository.UserRepository
import cz.deuss.userservice.domain.user.UserId
import io.github.oshai.kotlinlogging.KotlinLogging
import io.micronaut.context.annotation.Value
import io.micronaut.transaction.annotation.Transactional
import jakarta.inject.Singleton
import java.time.Duration
import java.time.LocalDateTime
import java.util.UUID
import kotlin.jvm.optionals.getOrNull

/**
 * Service for persisting, revoking and validating invite tokens
 */
@Singleton
open class InviteTokenPersistenceService(
    private val inviteTokenRepository: InviteTokenRepository,
    @Value("\${micronaut.security.token.generator.invite-token.expiration}")
    private val inviteTokenExpiry: Duration,
    private val userRepository: UserRepository,
) {

    private val logger = KotlinLogging.logger {}

    /**
     * Persist a new invite token when it's generated
     */
    fun createTokens(id: UserId, count: Int): List<UUID> {
        val user = userRepository.getById(id.id)
        logger.info { "User#${user?.id} is creating $count new invite tokens." }
        val expiresAt = LocalDateTime.now().plus(inviteTokenExpiry)

        val tokens = List (count) {
            InviteToken(
                createdByUser = user,
                expiresAt = expiresAt,
            )
        }
        return inviteTokenRepository.saveAll(tokens).map { it.id }
    }

    @Transactional
    open fun revokeToken(tokenId: UUID) {
        val token = findAndValidateToken(tokenId)
        token.revoke()
        inviteTokenRepository.update(token)
    }

    @Transactional
    open fun useToken(tokenId: UUID, userId: UUID) {
        findAndValidateToken(tokenId)
        inviteTokenRepository.updateRegisteredUserIdById(tokenId, userId)
    }

    fun findAndValidateToken(tokenId: UUID): InviteToken {
        val token = inviteTokenRepository.findById(tokenId).getOrNull()

        if (token == null || token.hasExpired() || token.registeredUserId != null || token.revoked) {
            throw NotFoundException("InviteToken#${tokenId} is invalid.")
        }

        return token
    }
}
