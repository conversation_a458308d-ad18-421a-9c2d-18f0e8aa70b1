package cz.deuss.userservice.mapping

import cz.deuss.platform.offchain.framework.authentication.Role
import cz.deuss.userservice.api.model.UserData
import cz.deuss.userservice.api.model.UserRole
import cz.deuss.userservice.database.model.DeussUser

//extends UserEntity With toUserData
fun DeussUser.toUserData(roles: Set<Role.Enum>) = UserData(
    id = id,
    email = email,
    firstName = firstName,
    lastName = lastName,
    phoneNumber = phoneNumber,
    residenceCountry = residenceCountry,
    birthDate = birthDate,
    createdAt = created,
    updatedAt = lastEdit,
    roles = roles.mapTo(HashSet()) { role -> role.toUserRole() }
)

fun Role.Enum.toUserRole(): UserRole {
    return when (this) {
        Role.Enum.USER -> UserRole.USER
        Role.Enum.ADMIN -> UserRole.ADMIN
        Role.Enum.ORIGINATOR -> UserRole.ORIGINATOR
    }
}
