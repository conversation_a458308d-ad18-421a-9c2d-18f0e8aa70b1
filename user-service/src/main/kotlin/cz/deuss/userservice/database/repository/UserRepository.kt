package cz.deuss.userservice.database.repository

import cz.deuss.userservice.database.model.DeussUser
import io.micronaut.data.annotation.Repository
import io.micronaut.data.jpa.repository.JpaRepository
import java.util.UUID

@Repository
interface UserRepository : JpaRepository<DeussUser, UUID> {
    fun getByEmail(email: String): Deuss<PERSON>ser?
    fun getById(id: UUID): DeussUser?
    fun getIdByEmail(email: String): UUID?
}
