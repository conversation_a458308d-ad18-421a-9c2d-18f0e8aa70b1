package cz.deuss.userservice.passkey.registration

import cz.deuss.platform.offchain.framework.authentication.AuthenticatedUserId
import cz.deuss.platform.offchain.framework.authentication.AuthenticationService
import cz.deuss.userservice.api.model.FinishAdditionalPasskeyRegistrationRequest
import cz.deuss.userservice.api.model.FinishPasskeyRegistrationInput
import cz.deuss.userservice.domain.user.toUserId
import cz.deuss.userservice.passkey.PasskeyCredential
import cz.deuss.userservice.passkey.PasskeyId
import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.shouldBe
import io.micronaut.http.HttpStatus
import io.mockk.clearMocks
import io.mockk.every
import io.mockk.mockk
import java.time.LocalDateTime
import java.util.UUID

class RegisterAdditionalPasskeyRestAdapterTest : FunSpec({

    val authenticatedUserId = AuthenticatedUserId(UUID.randomUUID())
    val email = "<EMAIL>"

    val registerAdditionalPasskeyService = mockk<RegisterAdditionalPasskeyService>()
    val tested = RegisterAdditionalPasskeyRestAdapter(registerAdditionalPasskeyService) {
        AuthenticationService.AuthenticationData(
            authenticatedUserId,
            email,
            emptySet(),
        )
    }

    beforeEach {
        clearMocks(registerAdditionalPasskeyService)
    }

    test("finish passkey registration") {
        val passkey = FinishPasskeyRegistrationInput(
            session = UUID.randomUUID(),
            attestationResponse = mockk(),
            friendlyName = "Pixel 9",
        )
        val req = FinishAdditionalPasskeyRegistrationRequest(passkey)

        val createdPasskeyCredential = PasskeyCredential(
            id = PasskeyId(UUID.randomUUID()),
            friendlyName = "My device",
            lastUsedAt = LocalDateTime.now(),
            lastUpdatedAt = LocalDateTime.now(),
        )
        every {
            registerAdditionalPasskeyService.finishAdditionalPasskeyRegistration(
                authenticatedUserId.toUserId(),
                passkey.session,
                passkey.attestationResponse,
                passkey.friendlyName,
            )
        } returns createdPasskeyCredential

        val response = tested.finishAdditionalPasskeyRegistration(req)
        response.status shouldBe HttpStatus.CREATED

        val actualPasskey = response.body().passkey
        actualPasskey.id shouldBe createdPasskeyCredential.id.id
        actualPasskey.friendlyName shouldBe createdPasskeyCredential.friendlyName
        actualPasskey.lastUpdatedAt shouldBe createdPasskeyCredential.lastUpdatedAt
        actualPasskey.lastUsedAt shouldBe createdPasskeyCredential.lastUsedAt
    }
})
