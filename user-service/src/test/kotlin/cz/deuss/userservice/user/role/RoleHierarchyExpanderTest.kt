package cz.deuss.userservice.user.role

import cz.deuss.platform.offchain.framework.authentication.Role
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.maps.shouldContainExactly
import io.kotest.matchers.throwable.shouldHaveMessage

class RoleHierarchyExpanderTest : FunSpec({

    test("expand hierarchy") {
        val roleHierarchy = mapOf(
            Role.Enum.USER to setOf(Role.Enum.USER),
            Role.Enum.ADMIN to setOf(Role.Enum.USER),
            Role.Enum.ORIGINATOR to setOf(Role.Enum.USER),
        )

        val expanded = RoleHierarchyExpander(roleHierarchy::getValue).fullyExpandRole()

        expanded shouldContainExactly mapOf(
            Role.Enum.USER to setOf(Role.Enum.USER),
            Role.Enum.ORIGINATOR to setOf(Role.Enum.ORIGINATOR, Role.Enum.USER),
            Role.Enum.ADMIN to setOf(Role.Enum.USER, Role.Enum.ADMIN),
        )
    }

    test("expand hierarchy - leaf role add itself to expanded roles") {
        val roleHierarchy = mapOf(
            Role.Enum.USER to setOf(),
            Role.Enum.ADMIN to setOf(Role.Enum.USER),
            Role.Enum.ORIGINATOR to setOf(Role.Enum.USER),
        )

        val expanded = RoleHierarchyExpander(roleHierarchy::getValue).fullyExpandRole()

        expanded shouldContainExactly mapOf(
            Role.Enum.USER to setOf(Role.Enum.USER),
            Role.Enum.ADMIN to setOf(Role.Enum.USER, Role.Enum.ADMIN),
            Role.Enum.ORIGINATOR to setOf(Role.Enum.USER, Role.Enum.ORIGINATOR)
        )
    }

    test("hierarchy with cycle fail to expand") {
        val roleHierarchy = mapOf(
            Role.Enum.USER to setOf(Role.Enum.ADMIN),
            Role.Enum.ADMIN to setOf(Role.Enum.USER),
            Role.Enum.ORIGINATOR to setOf(Role.Enum.USER)
        )

        shouldThrow<IllegalStateException> {
            RoleHierarchyExpander(roleHierarchy::getValue).fullyExpandRole()
        }.shouldHaveMessage(RoleHierarchyExpander.errorMessage(emptySet(), setOf()))
    }
})
