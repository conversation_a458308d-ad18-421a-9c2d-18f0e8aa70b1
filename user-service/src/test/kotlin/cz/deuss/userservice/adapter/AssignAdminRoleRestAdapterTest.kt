package cz.deuss.userservice.adapter

import cz.deuss.platform.offchain.framework.authentication.AuthenticatedUserId
import cz.deuss.platform.offchain.framework.authentication.Role
import cz.deuss.platform.offchain.framework.exceptions.NotFoundException
import cz.deuss.userservice.api.model.UserRole
import cz.deuss.userservice.domain.user.UserId
import cz.deuss.userservice.service.UserService
import cz.deuss.userservice.user.role.AssignedUserRolesService
import cz.deuss.userservice.user.role.ManageUserRoleService
import cz.deuss.userservice.user.role.RoleAssignment as DomainRoleAssignment
import cz.deuss.userservice.validation.Invalid
import cz.deuss.userservice.validation.Valid
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.shouldBe
import io.mockk.clearMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.time.LocalDateTime
import java.util.UUID

class AssignAdminRoleRestAdapterTest : FunSpec({

    val userService = mockk<UserService>()
    val manageUserRoleService = mockk<ManageUserRoleService>()
    val assignedUserRolesService = mockk<AssignedUserRolesService>()

    val assignerId = UserId(UUID.randomUUID())

    val tested = AssignRoleRestAdapter(
        userService,
        manageUserRoleService,
        assignedUserRolesService,
        { AuthenticatedUserId(assignerId.id) }
    )

    beforeTest { clearMocks(userService, manageUserRoleService, assignedUserRolesService) }

    test("assigns admin role") {
        val potentialAssignee = UUID.randomUUID()
        val assigneeId = UserId(potentialAssignee)
        val now = LocalDateTime.now()

        every { userService.validateExists(potentialAssignee) } returns Valid(assigneeId)

        every { manageUserRoleService.assignRole(assignerId, Role.Enum.ADMIN, assigneeId) } returns Unit

        val roleAssignment = DomainRoleAssignment(
            assignee = assigneeId,
            assigner = assignerId,
            role = Role.Enum.ADMIN,
            assigned = now
        )
        every { assignedUserRolesService.listAssignments(assigneeId) } returns listOf(roleAssignment)

        val response = tested.assignRole(potentialAssignee, Role.Enum.ADMIN)
        val actualAssignment = response.roleAssignment

        actualAssignment.assigneeId shouldBe assigneeId.id
        actualAssignment.assignerId shouldBe assignerId.id
        actualAssignment.assignedAt = now
        actualAssignment.role shouldBe UserRole.ADMIN

        verify(exactly = 1) { userService.validateExists(potentialAssignee) }
        verify(exactly = 1) { manageUserRoleService.assignRole(assignerId, Role.Enum.ADMIN, assigneeId) }
        verify(exactly = 1) { assignedUserRolesService.listAssignments(assigneeId) }
    }

    test("validation failure - user not found") {
        val assigneeUuid = UUID.randomUUID()
        every { userService.validateExists(assigneeUuid) } returns Invalid(assigneeUuid)

        shouldThrow<NotFoundException> {
            tested.assignRole(assigneeUuid, Role.Enum.ADMIN)
        }

        verify(exactly = 1) { userService.validateExists(assigneeUuid) }
        verify(exactly = 0) { manageUserRoleService.assignRole(any(), any(), any()) }
        verify(exactly = 0) { assignedUserRolesService.listAssignments(any()) }
    }

})
