package cz.deuss.userservice.adapter

import cz.deuss.platform.offchain.framework.authentication.Role
import cz.deuss.platform.offchain.framework.exceptions.ConflictException
import cz.deuss.platform.offchain.framework.exceptions.NotFoundException
import cz.deuss.userservice.api.model.UserRole
import cz.deuss.userservice.domain.user.UserId
import cz.deuss.userservice.service.UserService
import cz.deuss.userservice.user.role.AssignedRoles
import cz.deuss.userservice.user.role.AssignedUserRolesService
import cz.deuss.userservice.user.role.ManageUserRoleService
import cz.deuss.userservice.validation.Invalid
import cz.deuss.userservice.validation.Valid
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.FunSpec
import io.kotest.matchers.shouldBe
import io.mockk.clearMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.util.UUID

class UnassignAdminRoleRestAdapterTest : FunSpec({

    val userService = mockk<UserService>()
    val manageUserRoleService = mockk<ManageUserRoleService>()
    val assignedUserRolesService = mockk<AssignedUserRolesService>()

    val tested = UnassignRoleRestAdapter(
        userService,
        manageUserRoleService,
        assignedUserRolesService,
    )

    beforeTest { clearMocks(userService, manageUserRoleService, assignedUserRolesService) }

    test("unassigns admin role") {
        val potentialAssignee = UUID.randomUUID()
        val assigneeId = UserId(potentialAssignee)

        every { userService.validateExists(potentialAssignee) } returns Valid(assigneeId)
        every { assignedUserRolesService.assignedRoles(assigneeId) } returns AssignedRoles(setOf(Role.Enum.ADMIN))
        every { manageUserRoleService.unassign(assigneeId, Role.Enum.ADMIN) } returns Unit

        val response = tested.unassignRole(potentialAssignee, Role.Enum.ADMIN)

        response.unassignedRole shouldBe UserRole.ADMIN

        verify(exactly = 1) { userService.validateExists(potentialAssignee) }
        verify(exactly = 1) { assignedUserRolesService.assignedRoles(assigneeId) }
        verify(exactly = 1) { manageUserRoleService.unassign(assigneeId, Role.Enum.ADMIN) }
    }

    test("validation failure - user not found") {
        val assigneeUuid = UUID.randomUUID()
        every { userService.validateExists(assigneeUuid) } returns Invalid(assigneeUuid)

        shouldThrow<NotFoundException> {
            tested.unassignRole(assigneeUuid, Role.Enum.ADMIN)
        }

        verify(exactly = 1) { userService.validateExists(assigneeUuid) }
        verify(exactly = 0) { assignedUserRolesService.assignedRoles(any()) }
        verify(exactly = 0) { manageUserRoleService.unassign(any(), any()) }
    }

    test("validation failure - user does not have admin role") {
        val potentialAssignee = UUID.randomUUID()
        val assigneeId = UserId(potentialAssignee)

        every { userService.validateExists(potentialAssignee) } returns Valid(assigneeId)
        every { assignedUserRolesService.assignedRoles(assigneeId) } returns AssignedRoles(setOf(Role.Enum.USER))

        shouldThrow<ConflictException> {
            tested.unassignRole(potentialAssignee, Role.Enum.ADMIN)
        }

        verify(exactly = 1) { userService.validateExists(potentialAssignee) }
        verify(exactly = 1) { assignedUserRolesService.assignedRoles(assigneeId) }
        verify(exactly = 0) { manageUserRoleService.unassign(any(), any()) }
    }
})
