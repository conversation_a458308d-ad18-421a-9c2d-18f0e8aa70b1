micronaut:
  server:
    port: 8081
  security:
    authentication: bearer
    token:
      invite-token:
        generator:
          expiration: PT1H # 1 hour
      jwt:
        signatures:
          secret:
            generator:
              secret: my-super-secret-key-1234567890!01234
        generator:
          refresh-token:
            enabled: true
            secret: my-super-secret-key-1234567890!01234
            expiration: 2592000


passkey:
  rp-id: "localhost"
  rp-name: "Test Deuss services"
  origin: "http://localhost:63342"
  challenge-timeout: PT5M