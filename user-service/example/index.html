<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Passwordless Auth Demo</title>
    <script>
        async function startRegistration() {
            const email = document.getElementById('emailReg').value;
            const inviteToken = document.getElementById('inviteTokenReg').value;


            try {
                // Get registration options from server
                const optionsResponse = await fetch('http://localhost:8080/api/exposed/auth/register/passkey/start', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        email: email,
                        inviteToken: inviteToken
                    })
                });

                if (!optionsResponse.ok){
                    throw new Error('Failed to get registration options');
                }
                const options = await optionsResponse.json();

                // Create credential
                const credential = await navigator.credentials.create({
                    publicKey: {
                        challenge: base64ToUint8Array(options.challenge),
                        rp: {name: options.rp.name, id: options.rp.id},
                        user: {
                            id: base64ToUint8Array(options.user.id),
                            name: options.user.name,
                            displayName: options.user.displayName,
                        },
                        pubKeyCredParams: options.pubKeyCredParams,
                        authenticatorSelection: {
                            residentKey: 'required',
                            userVerification: 'required',
                            authenticatorAttachment: 'platform'
                        }
                    }
                });

                // Send to server
                const verificationResponse = await fetch('http://localhost:8080/api/exposed/auth/register/passkey/finish', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        session: options.session,
                        userData: {
                            first_name: document.getElementById('firstName').value,
                            last_name: document.getElementById('lastName').value,
                            phone_number: document.getElementById('phone').value,
                            residence_country: document.getElementById('country').value,
                            birth_date: document.getElementById('birthDate').value
                        },
                        attestationResponse: {
                            clientDataJSON: arrayBufferToBase64(credential.response.clientDataJSON),
                            attestationObject: arrayBufferToBase64(credential.response.attestationObject)
                        }
                    })
                });

                if (!verificationResponse.ok) throw new Error('Registration failed');
                document.getElementById('statusReg').textContent = 'Registration successful!';
                document.getElementById('statusReg').style.color = 'green';

                // Clear form
                ['emailReg', 'firstName', 'lastName', 'inviteTokenReg'].forEach(id =>
                    document.getElementById(id).value = ''
                );

            } catch (error) {
                document.getElementById('statusReg').textContent = 'Error: ' + error.message;
                document.getElementById('statusReg').style.color = 'red';
            }
        }

        async function startLogin() {
            try {
                // Get login challenge
                const optionsResponse = await fetch('http://localhost:8080/api/exposed/auth/login/passkey/start', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'}
                });

                if (!optionsResponse.ok) throw new Error('Login failed');
                const options = await optionsResponse.json();

                // Get assertion
                const assertion = await navigator.credentials.get({
                    publicKey: {
                        challenge: base64ToUint8Array(options.challenge),
                        rpId: options.rpId,
                        allowCredentials: [],// Let browser select resident key
                        userVerification: 'required'
                    }
                });

                // Verify with server
                const loginResponse = await fetch('http://localhost:8080/api/exposed/auth/login/passkey/finish', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        session: options.session,
                        assertionResponse: {
                            id: arrayBufferToBase64(assertion.rawId), // Use rawId here
                            rawId: arrayBufferToBase64(assertion.rawId), // Include rawId as well
                            response: {
                                clientDataJSON: arrayBufferToBase64(assertion.response.clientDataJSON),
                                authenticatorData: arrayBufferToBase64(assertion.response.authenticatorData),
                                signature: arrayBufferToBase64(assertion.response.signature),
                                userHandle: assertion.response.userHandle ? arrayBufferToBase64(assertion.response.userHandle) : null,
                            },
                        }
                    })
                });

                if (!loginResponse.ok) throw new Error('Login failed');
                const result = await loginResponse.json();

                document.getElementById('statusLogin').textContent = 'Login successful!';
                document.getElementById('statusLogin').style.color = 'green';
                alert(`Welcome ${result.user.email}!`);

            } catch (error) {
                document.getElementById('statusLogin').textContent = 'Error: ' + error.message;
                document.getElementById('statusLogin').style.color = 'red';
            }
        }

        // Encoding utilities
        function base64ToUint8Array(base64) {
            return Uint8Array.from(atob(base64), c => c.charCodeAt(0));
        }


        function arrayBufferToBase64(buffer) {
            return btoa(String.fromCharCode(...new Uint8Array(buffer)));
        }
    </script>
    <style>
        /* Simplified styling */
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }

        fieldset {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }

        legend {
            padding: 0 10px;
            font-weight: bold;
        }

        input {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        button {
            background: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        button:hover {
            background: #45a049;
        }

        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
<h1>Passwordless Authentication Demo</h1>

<fieldset>
    <legend>Registration</legend>
    <input type="email" id="emailReg" placeholder="Email (for registration)" required>
    <input type="text" id="firstName" placeholder="First name" required>
    <input type="text" id="lastName" placeholder="Last name" required>
    <input type="tel" id="phone" placeholder="Phone number">
    <input type="text" id="country" placeholder="Country">
    <input type="date" id="birthDate" placeholder="Birthdate">
    <input type="text" id="inviteTokenReg" placeholder="Invite token" required>
    <button onclick="startRegistration()">Register Passkey</button>
    <div id="statusReg" class="status"></div>
</fieldset>

<fieldset>
    <legend>Login</legend>
    <button onclick="startLogin()">Sign in with Passkey</button>
    <div id="statusLogin" class="status"></div>
</fieldset>
</body>
</html>