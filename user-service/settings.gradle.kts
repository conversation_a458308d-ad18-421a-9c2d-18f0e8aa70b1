rootProject.name = "user-service"

val deussGitlabTokenValue: String? by settings
val deussPlatformVersion = "1.7.1"
val yubicoWebauthnVersion = "2.6.0"
val springSecurityVersion = "6.3.0"
val commonLoggingVersion = "1.2"
val boucyCastleVersion = "1.81"

dependencyResolutionManagement {
    repositories {
        mavenCentral()
        configureDeussGitlabRepositories()
    }
    versionCatalogs {
        create("deuss") {
            from("cz.deuss:deuss-platform-offchain-catalog:$deussPlatformVersion")
        }
        create("yubicoWebauthnDeps"){
            library("yubicoWebauthnCore","com.yubico:webauthn-server-core:${yubicoWebauthnVersion}")
            library("yubicoWebauthnAttestation","com.yubico:webauthn-server-attestation:${yubicoWebauthnVersion}")
        }
        create("springSecurityDeps") {
            library("springSecurityCrypto", "org.springframework.security:spring-security-crypto:$springSecurityVersion")
            library("commonLogging", "commons-logging:commons-logging:$commonLoggingVersion")
            library("bouncyCastle", "org.bouncycastle:bcpkix-jdk18on:$boucyCastleVersion")
        }
    }
}

private fun RepositoryHandler.configureDeussGitlabRepositories() {
    val (tokenName, tokenValue) = provideCredentials()
    this.apply {
        maven {
            name = "deuss-platform-offchain-framework"
            url = uri("https://gitlab.nesad.fit.vutbr.cz/api/v4/projects/195/packages/maven")
            credentials(HttpHeaderCredentials::class) {
                name = tokenName
                value = tokenValue
            }
            authentication {
                create("header", HttpHeaderAuthentication::class)
            }
        }
    }
}

private fun provideCredentials(): Pair<String, String> {
    System.getenv("CI")?.run {
        logger.info("Running in CI environment")
        return "Job-Token" to System.getenv("CI_JOB_TOKEN")
    }
    val value = requireNotNull(deussGitlabTokenValue) {
        "Gitlab token named deussGitlabTokenValue is not set. Please checkout this wiki page https://gitlab.nesad.fit.vutbr.cz/bebi/offchain-microservices-core/offchain/-/wikis/gitlab-access-token"
    }
    logger.info("Running in local environment")
    return "Private-Token" to value
}
