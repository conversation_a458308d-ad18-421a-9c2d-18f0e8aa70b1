private val javaVersion: String by project

version = "0.1"
group = "cz.deuss"

plugins {
    alias(deuss.plugins.kotlin.noarg)
    alias(deuss.plugins.kotlin.allOpen)
    alias(deuss.plugins.kotlin.jvm)
    alias(deuss.plugins.google.ksp)
    alias(deuss.plugins.gradle.shadow)
    alias(deuss.plugins.micronaut.application)
    alias(deuss.plugins.micronaut.openapi)
}

dependencies {
    ksp("io.micronaut.data:micronaut-data-processor")
    ksp("io.micronaut:micronaut-http-validation")
    ksp("io.micronaut.serde:micronaut-serde-processor")
    ksp("io.micronaut.validation:micronaut-validation-processor")
    ksp("io.micronaut.openapi:micronaut-openapi")
    ksp("io.micronaut.security:micronaut-security-annotations")
    implementation("io.micronaut.kotlin:micronaut-kotlin-runtime")
    implementation("io.micronaut.serde:micronaut-serde-jackson")
    implementation("io.micronaut.reactor:micronaut-reactor")
    implementation("io.micronaut.reactor:micronaut-reactor-http-client")
    implementation("org.simplejavamail:simple-java-mail:8.12.6")
    implementation(deuss.bundles.kotlin)
    implementation(deuss.bundles.micronaut.validation)
    implementation(deuss.bundles.micronaut.data)
    implementation(deuss.bundles.micronaut.security)
    implementation(deuss.bundles.logging)
    implementation(deuss.platform.offchain.framework)
    implementation(yubicoWebauthnDeps.yubicoWebauthnCore)
    implementation(yubicoWebauthnDeps.yubicoWebauthnAttestation)
    implementation(springSecurityDeps.springSecurityCrypto)
    implementation("net.logstash.logback:logstash-logback-encoder")
    compileOnly("io.micronaut:micronaut-http-client")
    runtimeOnly(springSecurityDeps.commonLogging)
    runtimeOnly(springSecurityDeps.bouncyCastle)
    runtimeOnly("ch.qos.logback:logback-classic")
    runtimeOnly("com.fasterxml.jackson.module:jackson-module-kotlin")
    runtimeOnly("org.flywaydb:flyway-database-postgresql")
    runtimeOnly("org.postgresql:postgresql")
    runtimeOnly("org.yaml:snakeyaml")
    testImplementation(deuss.bundles.test.base)
    testImplementation(platform(deuss.test.containers.bom))
    testImplementation(deuss.bundles.test.containers)
    testImplementation("org.junit.jupiter:junit-jupiter-params")
    testImplementation(deuss.platform.offchain.test)
    testImplementation("com.fasterxml.jackson.dataformat:jackson-dataformat-cbor")
    testImplementation(deuss.io.mockk)
    testImplementation(deuss.bundles.test.kotest)
}

application {
    mainClass = "cz.deuss.userservice.ApplicationKt"
}
java {
    sourceCompatibility = JavaVersion.toVersion(javaVersion)
}

graalvmNative.toolchainDetection = false

micronaut {
    version = deuss.versions.micronaut.version.get()
    runtime("netty")
    testRuntime("junit5")
    processing {
        incremental(true)
        annotations("cz.deuss.userservice.*")
    }
    openapi {
        server(file("src/main/resources/openapi/user-service.yaml")) {
            apiPackageName = "cz.deuss.userservice.api"
            modelPackageName = "cz.deuss.userservice.api.model"
            useReactive = false
            useAuth = false
            lang = "kotlin"
            useBeanValidation = true
            dateTimeFormat = "LOCAL_DATETIME"
            useOptional = false
        }
    }
}

tasks.named<io.micronaut.gradle.docker.NativeImageDockerfile>("dockerfileNative") {
    jdkVersion = javaVersion
}
noArg {
    annotations(
        "jakarta.persistence.Entity",
        "jakarta.persistence.Embeddable"
    )
}
allOpen {
    annotations(
        "jakarta.persistence.Entity",
        "jakarta.persistence.MappedSuperclass",
        "jakarta.persistence.Embeddable",
    )
}

tasks {
    test {
        testLogging {
            showStandardStreams = true
            showStackTraces = true
            showCauses = true
            showExceptions = true
            events("passed", "skipped", "failed")
            exceptionFormat = org.gradle.api.tasks.testing.logging.TestExceptionFormat.FULL
        }
        useJUnitPlatform {
            includeEngines("junit-jupiter", "kotest")
        }
        reports {
            junitXml.outputLocation = file("${project.layout.buildDirectory.get()}/reports/tests/test/xml")
        }
    }
}
